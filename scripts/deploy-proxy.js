const { ethers, network, run } = require("hardhat");
require("dotenv").config();

async function main() {
    console.log("🚀 Completing Netronlink Proxy Deployment...");
    console.log("Network:", network.name);
    console.log("Chain ID:", network.config.chainId);

    const [deployer] = await ethers.getSigners();
    console.log("Deploying from account:", deployer.address);

    const balance = await deployer.provider.getBalance(deployer.address);
    console.log("Account balance:", ethers.formatEther(balance), "ETH");

    // Implementation contract address (already deployed and verified)
    const implementationAddress = "******************************************";
    console.log("Implementation contract:", implementationAddress);

    // Token distribution recipients and amounts
    const recipients = [
        process.env.PRESALE,
        process.env.DEVELOPMENT,
        process.env.MARKETING,
        process.env.STAKING,
        process.env.FINTECH,
        process.env.FUTURE,
        process.env.DAO,
    ];

    const amounts = [
        ethers.parseEther("********"), // 12%  presale
        ethers.parseEther("********"), // 15%  development
        ethers.parseEther("5000000"),  // 5%   marketing
        ethers.parseEther("********"), // 20%  staking
        ethers.parseEther("********"), // 10%  fintech
        ethers.parseEther("********"), // 14%  future
        ethers.parseEther("4000000"),  // 4%   DAO
    ];

    console.log("\n📋 Proxy Deployment Configuration:");
    console.log("Recipients:", recipients.length);
    console.log("Total allocation:", ethers.formatEther(amounts.reduce((a, b) => a + b, 0n)), "NTL");
    console.log("Team & Founder:", process.env.TEAM_FOUNDER);
    console.log("Contract Owner:", deployer.address);

    // Validate all addresses
    console.log("\n🔍 Validating addresses...");
    for (let i = 0; i < recipients.length; i++) {
        if (!ethers.isAddress(recipients[i])) {
            throw new Error(`Invalid recipient address ${i + 1}: ${recipients[i]}`);
        }
    }
    if (!ethers.isAddress(process.env.TEAM_FOUNDER)) {
        throw new Error(`Invalid TEAM_FOUNDER address: ${process.env.TEAM_FOUNDER}`);
    }
    console.log("✅ All addresses validated");

    try {
        // Get the Netronlink contract factory for encoding initialization data
        const NetronlinkFactory = await ethers.getContractFactory("Netronlink");

        // Encode initialization data
        console.log("\n🔨 Encoding initialization data...");
        const initData = NetronlinkFactory.interface.encodeFunctionData("initialize", [
            recipients,
            amounts,
            process.env.TEAM_FOUNDER,
            deployer.address
        ]);
        console.log("✅ Initialization data encoded");

        // Deploy Proxy Contract
        console.log("\n🔨 Deploying OwnedUpgradeabilityProxy...");
        const ProxyFactory = await ethers.getContractFactory("OwnedUpgradeabilityProxy");

        const proxy = await ProxyFactory.deploy({
            gasLimit: 1500000, // 1.5M gas limit
            maxFeePerGas: ethers.parseUnits("20", "gwei"), // 20 gwei max
            maxPriorityFeePerGas: ethers.parseUnits("2", "gwei"), // 2 gwei priority
        });

        console.log("⏳ Waiting for proxy deployment...");
        const proxyReceipt = await proxy.deploymentTransaction().wait();
        const proxyAddress = await proxy.getAddress();

        console.log("✅ Proxy deployed to:", proxyAddress);
        console.log("📄 Transaction hash:", proxyReceipt.hash);
        console.log("⛽ Gas used:", proxyReceipt.gasUsed.toString());

        // Set implementation and initialize
        console.log("\n🔨 Setting implementation and initializing...");
        const upgradeToAndCallTx = await proxy.upgradeToAndCall(implementationAddress, initData, {
            gasLimit: 2000000, // 2M gas limit
            maxFeePerGas: ethers.parseUnits("20", "gwei"), // 20 gwei max
            maxPriorityFeePerGas: ethers.parseUnits("2", "gwei"), // 2 gwei priority
        });

        console.log("⏳ Waiting for initialization...");
        const initReceipt = await upgradeToAndCallTx.wait();
        console.log("✅ Implementation set and initialized");
        console.log("📄 Init transaction hash:", initReceipt.hash);
        console.log("⛽ Init gas used:", initReceipt.gasUsed.toString());

        // Get the proxied contract instance
        const netronlink = await ethers.getContractAt("Netronlink", proxyAddress);

        // Verify deployment
        console.log("\n🔍 Verifying deployment...");
        const name = await netronlink.name();
        const symbol = await netronlink.symbol();
        const totalSupply = await netronlink.totalSupply();
        const owner = await netronlink.owner();

        console.log("\n📊 Deployment Summary:");
        console.log("========================================");
        console.log("🏗️  Network:", network.name);
        console.log("🎯 Implementation:", implementationAddress);
        console.log("🎯 Proxy (Main Contract):", proxyAddress);
        console.log("📛 Token Name:", name);
        console.log("🔤 Token Symbol:", symbol);
        console.log("💰 Total Supply:", ethers.formatEther(totalSupply), "NTL");
        console.log("👤 Contract Owner:", owner);
        console.log("========================================");

        // Verify some recipient balances
        console.log("\n💰 Token Distribution Verification:");
        for (let i = 0; i < Math.min(3, recipients.length); i++) {
            const balance = await netronlink.balanceOf(recipients[i]);
            console.log(`  Recipient ${i + 1}: ${ethers.formatEther(balance)} NTL`);
        }

        // Check team founder balance
        const teamBalance = await netronlink.balanceOf(process.env.TEAM_FOUNDER);
        console.log(`  Team & Founder: ${ethers.formatEther(teamBalance)} NTL`);

        console.log("\n🎉 Proxy deployment completed successfully!");
        console.log("💡 Use proxy address for all interactions:", proxyAddress);

        // Schedule verification after 10 seconds
        console.log("\n⏳ Scheduling proxy verification in 10 seconds...");

        setTimeout(async () => {
            try {
                console.log("\n🔍 Starting automatic proxy verification...");

                await run("verify:verify", {
                    address: proxyAddress,
                    constructorArguments: [],
                    contract: "contracts/OwnedUpgradeabilityProxy.sol:OwnedUpgradeabilityProxy"
                });

                console.log("✅ Proxy contract verified successfully!");
                console.log(`https://etherscan.io/address/${proxyAddress}#code`);

            } catch (error) {
                if (error.message.includes("already been verified")) {
                    console.log("✅ Proxy contract was already verified!");
                } else {
                    console.log("⚠️ Proxy verification failed:", error.message);
                    console.log("You can verify manually on Etherscan:");
                    console.log(`- Contract Address: ${proxyAddress}`);
                    console.log(`- Constructor Args: ${implementationAddress}, ${initData}`);
                }
            }
        }, 10000);

        return {
            implementation: implementationAddress,
            proxy: proxyAddress,
            token: proxyAddress // Main contract address
        };

    } catch (error) {
        console.error("\n❌ Proxy deployment failed:", error);
        throw error;
    }
}

main()
    .then((result) => {
        console.log("\n✅ Proxy deployment script completed successfully!");
        console.log("🎯 Main Contract Address:", result.proxy);
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n❌ Proxy deployment failed:", error.message);
        process.exit(1);
    });
